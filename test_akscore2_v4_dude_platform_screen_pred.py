import os, pickle
import numpy as np
import argparse

from sklearn.metrics import mean_squared_error, mean_absolute_error, auc
from torch_geometric.nn import global_mean_pool
from torch_geometric.loader import DataLoader
from torch_geometric.data import Data

import torch
import torch.nn.functional as F
from torch import nn
from torch.nn import Linear
from torch_geometric.nn import GATv2Conv
from torch_geometric.data import Dataset
from torch_geometric.data import batch as pyg_batch_func
from sklearn.metrics import roc_auc_score

import random
import time
import gzip
from itertools import chain


os.environ['CUDA_LAUNCH_BLOCKING'] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# DEVICE = torch.device('cpu')

BIND_AFF_MEAN = -0  # -8.7037,  -6
BIND_AFF_STD = 1  # 2.5375,    3


def one_hot_encoding(x, boundary, num_classes):
    one_hot_code = F.one_hot(torch.tensor(np.digitize(x, boundary)), num_classes=num_classes)
    return one_hot_code


class GATv2(torch.nn.Module):
    def __init__(self, node_dim, edge_dim, num_layers=5, hidden_dim=64):
        super().__init__()
        self.num_layers = num_layers
        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim

        self.node_to_hidden = Linear(self.node_dim, self.hidden_dim)
        self.dropout_layer = nn.Dropout(0.1)


        self.protein_gnn_layers = []
        for num in range(self.num_layers):
            self.protein_gnn_layers.append(GATv2Conv(self.hidden_dim, self.hidden_dim, edge_dim=self.edge_dim))
        self.protein_gnn_layers = nn.ModuleList(self.protein_gnn_layers)

        self.ligand_gnn_layers = []
        for num in range(self.num_layers//2+1):
            self.ligand_gnn_layers.append(GATv2Conv(self.hidden_dim, self.hidden_dim, edge_dim=self.edge_dim))
        self.ligand_gnn_layers = nn.ModuleList(self.ligand_gnn_layers)


        self.graph_dec_rmsd = nn.Sequential(nn.Linear(self.hidden_dim*2, self.hidden_dim*2),
                                       nn.ReLU(),
                                       nn.Dropout(0.1),
                                       nn.Linear(self.hidden_dim*2, self.hidden_dim),
                                       nn.ReLU(),
                                       nn.Dropout(0.1),
                                       nn.Linear(self.hidden_dim, 1),)


    def forward(self, graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch):

        x, edge_index, edge_attr, batch = graph_batch.x, graph_batch.edge_index, graph_batch.edge_attr, graph_batch.batch
        protein_x, protein_edge_index, protein_edge_attr, protein_batch = protein_graph_batch.x, protein_graph_batch.edge_index, protein_graph_batch.edge_attr, protein_graph_batch.batch
        ligand_x, ligand_edge_index, ligand_edge_attr, ligand_batch = ligand_graph_batch.x, ligand_graph_batch.edge_index, ligand_graph_batch.edge_attr, ligand_graph_batch.batch
        protein_ligand_x, protein_ligand_edge_index, protein_ligand_edge_attr, protein_ligand_batch = protein_ligand_graph_batch.x, protein_ligand_graph_batch.edge_index, protein_ligand_graph_batch.edge_attr, protein_ligand_graph_batch.batch

        protein_x = self.node_to_hidden(protein_x)
        ligand_x = self.node_to_hidden(ligand_x)

        for layer in self.protein_gnn_layers:
            protein_x = layer(protein_x, protein_edge_index, protein_edge_attr)
            protein_x = F.relu(protein_x)
            protein_x = self.dropout_layer(protein_x)

        for layer in self.ligand_gnn_layers:
            ligand_x = layer(ligand_x, ligand_edge_index, ligand_edge_attr)
            ligand_x = F.relu(ligand_x)
            ligand_x = self.dropout_layer(ligand_x)


        protein_x_feat = global_mean_pool(protein_x, protein_batch)  # [batch_size, hidden_channels]
        ligand_x_feat = global_mean_pool(ligand_x, ligand_batch)  # [batch_size, hidden_channels]

        protein_ligand_x_cat = torch.concat((protein_x_feat, ligand_x_feat), dim=-1)

        rmsd_logits = self.graph_dec_rmsd(protein_ligand_x_cat)

        return rmsd_logits





class akscore2_dataset(Dataset):
    def __init__(self, data_dir):
        super(akscore2_dataset, self).__init__()

        self.data_dir = data_dir
        self.graph_paths = []

#         self.graph_paths = [os.path.join(self.data_dir, f) for f in os.listdir(self.data_dir) if f.endswith(".pkl.gz")]
        
        ## graph one
#         self.graph_paths = [os.path.join(self.data_dir, f) for f in os.listdir(self.data_dir) if f.endswith(".pkl")]
        
        ## glide sp
        self.graph_paths = [os.path.join(self.data_dir, f) for f in os.listdir(self.data_dir) if f.endswith(".pkl.gz")]
        
        
        
    def len(self):
        # return 200

        return len(self.graph_paths)

    
    
    def graph_modification(self, graph):
        x, edge_index, edge_attr = graph.x.detach().clone(), graph.edge_index.detach().clone(), graph.edge_attr.detach().clone()

        protein_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([1, 0, 0])).all(dim=1))[0]
        ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 1, 0])).all(dim=1))[0]
        protein_ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 0, 1])).all(dim=1))[0]
        
        
        #         #### remove docking feature for platform app
        x = torch.concat((x[:,:-5], x[:,-4:]),axis=1)
        edge_attr = edge_attr[:,3:-9]
        
        
        protein_edge_index = edge_index[:, protein_edge_attr_idx]
        ligand_edge_index = edge_index[:, ligand_edge_attr_idx]
        protein_ligand_edge_index = edge_index[:, protein_ligand_edge_attr_idx]

        protein_ligand_node_sep_idx = torch.min(ligand_edge_index)

        protein_x = x[:protein_ligand_node_sep_idx, :]
        ligand_x = x[protein_ligand_node_sep_idx:, :]

        protein_graph = Data(x=protein_x, edge_index=protein_edge_index, edge_attr=edge_attr[protein_edge_attr_idx,:])
        ligand_graph = Data(x=ligand_x, edge_index=ligand_edge_index-torch.min(ligand_edge_index), edge_attr=edge_attr[ligand_edge_attr_idx,:])

        protein_ligand_edge_attr = edge_attr[protein_ligand_edge_attr_idx,:]
        

        protein_ligand_graph = Data(x=x, edge_index=protein_ligand_edge_index, edge_attr=protein_ligand_edge_attr)

        return graph, protein_graph, ligand_graph, protein_ligand_graph

    
    
    
    def get(self, idx):
        graph_path = self.graph_paths[idx]
        
#         with gzip.open(graph_path, 'rb') as f:
#             pdb_id_graphs = pickle.load(f)

        ## graph_one
#         with open(graph_path, 'rb') as f:
#             pdb_id_graphs = [pickle.load(f)]
        
#         ### glide sp
        with gzip.open(graph_path, 'rb') as f:
            pdb_id_graphs = pickle.load(f)
        
        
        graph_list = []
        protein_graph_list = []
        ligand_graph_list = []
        protein_ligand_graph_list = []
        for graph in pdb_id_graphs:
            graph, protein_graph, ligand_graph, protein_ligand_graph = self.graph_modification(graph)
            
            ### glide sp
            graph.name = os.path.split(graph_path)[-1].split(".pkl.gz")[0]
            
            
            graph_list.append(graph)
            protein_graph_list.append(protein_graph)
            ligand_graph_list.append(ligand_graph)
            protein_ligand_graph_list.append(protein_ligand_graph)

        graph_list_batch = pyg_batch_func.Batch.from_data_list(graph_list)
        protein_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_graph_list)
        ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(ligand_graph_list)
        protein_ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_ligand_graph_list)

        return graph_list_batch, protein_graph_list_batch, ligand_graph_list_batch, protein_ligand_graph_list_batch


def cal_EF(actives, preds, result, prefer="negative", percents=[0.01, 0.05]):
    # actives = [True] * 100 + [False] * 200
    # preds = np.random.rand(300)
    # result = {}

    actives = np.array(actives)
    preds = np.array(preds)
    idx = np.argsort(preds)

    if prefer == "negative":
        preds_sorted = np.array(preds)[idx]
        actives_sorted = np.array(actives)[idx]
    else:
        preds_sorted = np.array(preds)[idx][::-1]
        actives_sorted = np.array(actives)[idx][::-1]

    total_actives_num = len(np.where(actives == True)[0])
    print(f"total actives num: {total_actives_num}")
    total_compounds_num = len(actives)

    for percent in percents:
        compounds_at_percent = round(total_compounds_num * percent)
        actives_at_percent = len(np.where(actives_sorted[:compounds_at_percent] == True)[0])
        result[f"EF{percent}"] = (actives_at_percent / compounds_at_percent) * (total_compounds_num / total_actives_num)

    return result



def save_result(result_save_dir, result, target_name):
        #### save dir

    result_save_dir = os.path.join(result_save_dir, "glide_sp")
    result_save_dir_1 = result_save_dir + "_rmsd"
    if not os.path.exists(result_save_dir_1):
        os.makedirs(result_save_dir_1)
    result_save_path = os.path.join(result_save_dir_1, target_name + '.txt')
    with open(result_save_path, 'w') as f:
        for i, _ in enumerate(result['names']):
            name = result['names'][i]
            name = name.split("_")[0] +"_" +name.split("_")[-1]
            f.write(f"{name}\t{i}\t{result['preds'][i]:.4f}\n")

if __name__ == '__main__':

    parser = argparse.ArgumentParser(description='Test Graph based Protein-Ligand Binding Affinity')
    parser.add_argument('--screen_dir', default=r'/Arontier/People/junsuha/akscore_project/DUD_E', type=str, help='data path')
    parser.add_argument('--target_name', default='', type=str, help='target name')

    parser.add_argument('--result_save_dir', default=r'', type=str, help='result save directory')
    parser.add_argument('--model_path', default=r'', type=str, help='model path')

    parser.add_argument('--node_dim', default=72, type=int, help="Input size of layer's node")
    parser.add_argument('--edge_dim', default=12, type=int, help='edge dimension')
    parser.add_argument('--hidden_dim', default=256, type=int, help='Hidden layer size')
    parser.add_argument('--num_layers', default=5, type=int, help='number of gnn layer')

    parser.add_argument('--batch_size', default=4, type=int, help='batch size')
    parser.add_argument('--num_workers', default=4, type=int, help="cpu worker number")

    args = parser.parse_args()
    print(args)

    model = GATv2(args.node_dim, args.edge_dim, num_layers=args.num_layers, hidden_dim=args.hidden_dim).to(DEVICE)

    if os.path.isfile(args.model_path):
        ch = torch.load(args.model_path)
        model.load_state_dict(ch['model'])
        model.to(DEVICE)
        model.eval()

    if args.result_save_dir == "":
        result_save_dir = args.model_path[:-4]

    else:
        result_save_dir = args.result_save_dir

    if not os.path.exists(result_save_dir):
        os.makedirs(result_save_dir)
    
    
    if args.target_name:
        target_names = [args.target_name]

        

    print(target_names)
    print("\n\n\n")

    for target_name in target_names:
        print(target_name)

        result = {
            "preds": [],
            "names": [],
            "pdb_id": target_name,
        }

        target_dir = os.path.join(args.screen_dir, target_name)
        
        
#         target_dir = os.path.join(target_dir, "graph")

        ### graph one
#         target_dir = os.path.join(target_dir, "graph_one")
        
        
        ### glide sp
        target_dir = os.path.join(target_dir, "glide_graph_one")

        
        dataset = akscore2_dataset(target_dir)
        loader = DataLoader(dataset, batch_size=args.batch_size, shuffle=False, num_workers=args.num_workers)

        with torch.no_grad():
            for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(
                    loader):

                print(f"{target_name} {idx}/{len(loader)}")
                rmsd_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE),
                                    ligand_graph_batch.to(DEVICE), protein_ligand_graph_batch.to(DEVICE))
                rmsd_logits = torch.sigmoid(rmsd_logits)
                result["preds"].extend([x.item() for x in rmsd_logits])
                result["names"].extend(list(chain(*graph_batch.name)))
                

        save_result(result_save_dir, result, target_name)







