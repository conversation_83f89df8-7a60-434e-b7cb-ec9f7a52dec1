# `input_module_my.py` 脚本说明书

## 1. 脚本概述

`input_module_my.py` 是一个用于生物信息学和计算化学的Python脚本，专门为 **Akscore2** 项目设计。其核心功能是将蛋白质（受体）和配体（小分子）的3D结构信息，从标准的化学文件格式（如 PDB, SDF, MOL2, DLG）转换为图神经网络（GNN）可以直接处理的**图数据结构**。

这个脚本是典型的**数据预处理管道 (Data Preprocessing Pipeline)**，它完成了从原始数据到机器学习模型的输入的关键一步，使得GNN能够学习和预测分子间的相互作用。

---

## 2. 依赖项

为了成功运行此脚本，你需要一个配置了以下核心库的Python环境：

### Python库:
*   **RDKit**: 用于处理和计算分子化学特征的核心库。
*   **Open Babel (`pybel`)**: 用于不同化学文件格式之间的转换。
*   **Meeko**: 用于处理 PDBQT 文件格式，这是AutoDock等对接软件常用的格式。
*   **PyTorch**: 核心的深度学习框架。
*   **PyTorch Geometric (`torch_geometric`)**: 基于PyTorch的图神经网络库，脚本最终输出的数据格式就是为其量身定做的。
*   **NumPy** & **Pandas**: 用于数值计算和数据处理。

### 外部工具 (潜在依赖):
*   脚本中部分函数调用了外部命令行工具，如 `gnss_dock2` 和 Schrodinger 套件的 `pdbconvert`。如果需要使用相关功能，必须确保这些工具已安装并配置在系统环境中。

---

## 3. 核心功能详解

### 3.1. 文件I/O与格式转换

脚本能够处理多种分子文件格式，并能进行灵活的转换。

*   **读取函数**: `Mol22mol`, `sdf2mol`, `pdb2mol`, `pdbqt2mol` 等函数利用 RDKit 和 Open Babel 将不同格式的文件读入为 RDKit 的 `Mol` 对象，这是后续所有分析的基础。
*   **多分子文件分割**: `pdb_list_cut`, `sdf_list_cut` 等函数可以将一个包含多个分子的文件（例如一个SDF文件）分割成多个独立的PDB文件，方便逐一处理。
*   **对接结果解析**: `read_pdb_block_from_pdb` 和 `poss2data` 等函数专门用于解析对接软件（如AutoDock Vina或GNINA）生成的 `.dlg` 或 `.pdbqt` 文件，能够抽取出每个对接构象（pose）的坐标、能量得分等信息。

### 3.2. 蛋白质口袋提取 (`make_pocket`)

这是至关重要的一步，旨在减少计算量并让模型聚焦于关键区域。

1.  **输入**: 整个蛋白质的PDB文件和单个配体的三维坐标。
2.  **过程**: 脚本会计算蛋白质中每个原子与配体所有原子之间的距离。
3.  **筛选**: 只有与配体最近距离小于预设阈值（`distance`，默认为5Å）的蛋白质残基（及其所有原子）会被保留下来。
4.  **输出**: 一个只包含结合口袋区域原子的新PDB数据块（字符串格式）。

### 3.3. 特征工程 (`Featurize` 类)

这是脚本的“大脑”，它定义了如何将原子和化学键翻译成GNN能理解的数字向量。

#### **节点特征 (`AtomNode`)**
为图中的每个原子（节点）生成一个包含丰富化学信息的特征向量。主要特征包括：
*   **原子类型**: One-hot编码 (C, H, O, N, S, P, ...)。
*   **拓扑信息**: 成键数（degree）、环信息（is in ring）、芳香性（is aromatic）。
*   **化学属性**: 氢原子数、价态、杂化类型 (sp, sp2, sp3)、形式电荷。
*   **物理化学属性**:
    *   **疏水性**: 对蛋白质原子，从一个预定义的字典 `prot_atom_hydrophobicity` 查询；对配体原子，则基于Gasteiger电荷判断。
    *   **药效团特征**: 通过SMARTS匹配，判断原子是否是**氢键供体**、**氢键受体**、**酸性基团**或**碱性基团**。

#### **边特征 (`CovalentEdge` & `PL_NonCovalentEdge`)**
为原子间的连接（边）生成特征向量，分为两种类型：

1.  **共价键 (Covalent Bonds)**:
    *   用于连接蛋白质内部原子，或配体内部原子。
    *   特征包括：键类型 (单键, 双键, 三键, 芳香键)、立体化学、是否在环内、是否共轭。

2.  **非共价相互作用 (Non-Covalent Interactions)**:
    *   这是脚本的亮点，用于连接蛋白质和配体原子。
    *   **相互作用类型**: 脚本编码了多种重要的分子间相互作用，包括：
        *   **疏水作用**
        *   **氢键** (区分了蛋白质作为供体和配体作为受体，反之亦然)
        *   **离子键** (盐桥，同样区分了方向)
    *   **距离信息**: 两个原子间的距离被离散化（分箱）为多个类别（例如 <2Å, <4Å, <6Å, <8Å），并进行one-hot编码。

### 3.4. 图的构建 (`mol_2_graph` 和 `make_graph`)

这些函数是整个流程的调度中心。

*   `mol_2_graph` 为单个蛋白质-配体复合物（构象）构建图。
*   `make_graph` 的逻辑更完整，用于为一个训练实例（通常对应一个PDB ID）生成一个包含多种构象的图数据列表。它能够整合：
    *   **天然构象 (Native)**: 晶体结构中的真实结合模式，RMSD被设为0。
    *   **对接构象 (Docked)**: 通过对接软件生成的构象。
    *   **诱骗分子 (Decoys/Cross)**: 作为负样本，用于提高模型的区分能力。

最终，所有特征向量和图的连接信息（`edge_index`）都被转换成`torch.Tensor`，并打包成一个 `torch_geometric.data.Data` 对象。

---

## 4. 如何运行

### 基本用法:
脚本通过命令行接收参数运行。

```bash
python input_module_my.py --input <PDB_ID> --distance <distance_in_angstrom>
```

*   `--input`: 指定要处理的PDB ID（例如 `1e66`）。脚本内部的硬编码路径会基于这个ID去查找所需文件。
*   `--distance`: 定义口袋范围的距离阈值，默认为 `5` (埃)。

### **重要提示：硬编码路径问题**

当前版本的脚本 (`input_module_my.py`) 在 `native_run` 和 `make_graph` 函数中包含了**大量写死的（hardcoded）文件路径**，例如 `/Arontier/Projects/AKscore2/...`。

这意味着，该脚本**无法在其他计算机或不同的目录结构下直接运行**。使用者必须手动修改这些路径，使其指向自己本地的数据存储位置。为了通用性，强烈建议将这些路径修改为通过命令行参数传入。

---

## 5. 输出

脚本的主要输出是保存在指定目录下的 `.pkl` 或 `.pkl.gz` 文件。每个文件包含一个或多个 `torch_geometric.data.Data` 对象。

一个典型的 `Data` 对象包含以下属性：
*   `data.x`: 节点特征矩阵，形状为 `[num_nodes, num_node_features]`。
*   `data.edge_index`: 图的连接信息（COO格式），形状为 `[2, num_edges]`。
*   `data.edge_attr`: 边的特征矩阵，形状为 `[num_edges, num_edge_features]`。
*   `data.y`: 标签数据，通常是该构象的 RMSD 值。
*   `data.bind`: 标签数据，通常是结合亲和力（binding affinity）。
*   `data.name`: 该数据点的唯一标识符。

这些文件可以直接被 Akscore2 的训练脚本加载和使用。

---

## 6. 函数调用逻辑链条

### 6.1. 主要数据流程

`input_module_my.py` 的函数调用遵循以下主要逻辑链条：

#### **数据预处理阶段**
```
preprocess()
├── pdb_list_cut() / sdf_list_cut() / mol2_list_cut()  # 根据输入格式选择
├── 文件格式转换和分割
└── 返回配体文件列表和中心坐标
```

#### **核心图构建阶段**
```
make_graph()
├── 读取结合亲和力数据
├── mol_2_graph()  # 核心图构建函数
│   ├── poss2data()  # 解析PDBQT数据
│   │   ├── pdbqt2mol()  # PDBQT转RDKit分子
│   │   └── 提取原子坐标和对接得分
│   ├── make_pocket()  # 提取蛋白质口袋
│   ├── pdb2data()  # 解析蛋白质PDB数据
│   ├── Featurize.AtomNode()  # 生成原子节点特征
│   ├── calculateDistance()  # 计算距离矩阵
│   ├── Featurize.CovalentEdge()  # 生成共价键边特征
│   ├── Featurize.PL_NonCovalentEdge()  # 生成非共价相互作用边特征
│   └── 转换为PyTorch张量
└── 保存为pickle文件
```

### 6.2. 关键函数依赖关系

#### **文件I/O函数链**
- `_file_reader()` ← 被多个文件读取函数调用
- `read_pdb_block_from_pdb()` ← `pdb_list_cut()`
- `Mol22mol()` ← `mol2_list_cut()`
- `sdf2mol()` ← `sdf_list_cut()`

#### **分子转换函数链**
- `pdb2mol()` ← `pdb2data()`
- `rdkit_mol_TO_pdbqt()` ← 分子格式转换
- `pdbqt2mol()` ← `poss2data()`

#### **特征化函数链**
```
Featurize类
├── __init__()  # 初始化SMARTS模式
├── AtomNode()  # 原子节点特征
│   └── one_hot()  # 独热编码
├── CommonEdge()  # 边类型标识
├── CovalentEdge()  # 共价键特征
│   └── CommonEdge()
└── PL_NonCovalentEdge()  # 非共价相互作用特征
    └── CommonEdge()
```

#### **数据处理函数链**
- `poss2data()` ← `mol_2_graph()`
- `calculateDistance()` ← `mol_2_graph()`
- `make_pocket()` ← `mol_2_graph()`
- `pdb2data()` ← `mol_2_graph()`

### 6.3. 辅助功能函数

#### **聚类和评估**
- `cluster_list()` - 从DLG文件提取聚类信息
- `dlg_2_rmsd()` - 计算RMSD值
- `PDBQT_native()` - 提取天然配体能量

#### **配置和工具**
- `get_default_config()` - 获取Meeko默认配置
- `one_hot()` - 独热编码工具函数

### 6.4. 数据流向图

```
输入文件 → preprocess() → 分子文件列表
                ↓
蛋白质PDB + 配体PDBQT → mol_2_graph() → 图特征张量
                ↓
        PyTorch Geometric Data对象
                ↓
            保存为.pkl文件
```

### 6.5. 错误处理机制

脚本在关键节点实现了异常处理：
- `mol_2_graph()` 中的 try-except 块处理单个配体的处理失败
- `pdbqt2mol()` 中的格式转换失败处理
- `make_pocket()` 中的口袋提取验证

这种设计确保即使部分数据处理失败，整个流程仍能继续处理其他有效数据。

---

## 7. 输出文件分类说明 (Output File Classification)

当脚本成功运行后，会在输出目录（例如 `data_5A_output/1e66/`）下生成大量的 `.pkl` 文件。这些文件是图数据对象，其命名格式 `pdb-id_form-id_index.pkl` 蕴含了重要的分类信息。

文件名 `1e66_X_Y.pkl` 可以解析为 `{pdb}_{form_id}_{num}`:
- `1e66`: 这是系统的 PDB ID。
- `{form_id}`: **分类标识符**，代表了配体的类型。
- `{num}`: 该类型下的唯一编号或索引。

根据 `{form_id}`，这些文件可以分为以下四个核心类别：

### 类别 1: `form_id = 0` (天然构象)
*   **文件示例**: `1e66_0_0.pkl`
*   **含义**: 代表 **天然构象 (Native Pose)**。它是从晶体结构中得到的蛋白质和配体的真实结合模式，是评估其他构象准确性的黄金标准。
*   **特点**:
    *   在图数据中，其 RMSD (均方根偏差) 被设为 `0.0`。
    *   每个PDB ID只有一个此类别的文件。

### 类别 2: `form_id = 1` (对接构象)
*   **文件示例**: `1e66_1_0.pkl`, `1e66_1_1.pkl`, ...
*   **含义**: 代表 **对接构象 (Docked Poses)**。它们是通过将天然配体“重新对接”(redocking)到其自身的结合口袋中生成的多种可能姿态。
*   **特点**:
    *   用于训练模型学习区分“好”的对接姿态（低 RMSD）和“坏”的对接姿态（高 RMSD）。
    *   通常是数量最多的类别。

### 类别 3: `form_id = 2` (随机诱骗分子)
*   **文件示例**: `1e66_2_0.pkl`, `1e66_2_1.pkl`, ...
*   **含义**: 代表 **随机诱骗分子 (Random Decoys)**。这些分子在物理化学性质上与天然配体相似，但拓扑结构不同，理论上不应与目标蛋白结合。
*   **特点**:
    *   作为负样本，用于训练模型区分真正的活性配体和不相关的分子。
    *   RMSD 通常被设为一个固定的高值（例如 `3.0`）。

### 类别 4: `form_id = 3` (交叉对接诱骗分子)
*   **文件示例**: `1e66_3_0.pkl`, `1e66_3_1.pkl`, ...
*   **含义**: 代表 **交叉对接诱骗分子 (Cross-Docked Decoys)**。这些是来自其他蛋白质-配体系统的配体，被强行对接到当前蛋白质的口袋中。
*   **特点**:
    *   同样作为负样本，用于模拟更真实的虚拟筛选场景，提高模型的泛化能力。

### 总结
这四个类别的数据共同构成了一个全面的训练和验证集：
- **类别 0 和 1** 教会模型识别**正确**的结合模式。
- **类别 2 和 3** 教会模型排除**错误**的分子。

这种数据组合使得训练出的模型不仅能预测结合亲和力，还能评估对接姿势的质量，从而在虚拟筛选中表现得更好。

---

## 8. 蛋白质PDB文件处理流程详解

`input_module_my.py` 脚本通过一系列精心设计的步骤来处理蛋白质PDB文件，最终将其转换为图神经网络可以理解的格式。整个流程始于`native_run`函数，它根据输入的PDB ID（例如`1e66`）构建一个指向完整蛋白质PDB文件的路径。在最新版本的脚本中，这个路径是 `f'{INPUT_data_dir}/redocking_general_set/{pdb}/{pdb}_protein.pdb'`。这个文件是所有后续蛋白质处理的起点。

处理流程可以分为以下几个关键步骤：

### 第1步：从完整蛋白质中“切割”出结合口袋 (`make_pocket`函数)

为了让模型聚焦于最重要的相互作用区域并减少计算量，脚本首先会从包含数千个原子的完整蛋白质结构中，精确地提取出与配体直接接触的“结合口袋”。

*   **输入**: 完整的蛋白质PDB文件路径 和 配体分子的原子坐标。
*   **过程**:
    1.  脚本逐行读取蛋白质PDB文件，筛选出所有重原子（非氢原子、非水分子）并记录它们的坐标、残基名、链ID等信息。
    2.  它遍历蛋白质中的**每一个原子**。
    3.  对于每个蛋白质原子，它会计算该原子与**配体中所有原子**的距离，并找出其中的最短距离。
    4.  **核心逻辑**: 如果这个最短距离小于或等于一个预设的阈值（`distance`参数，默认为5Å），那么这个蛋白质原子所在的整个**氨基酸残基**就会被标记为“口袋残基”。
    5.  脚本收集所有被标记的“口袋残基”。
*   **输出**: 一个全新的、只包含这些“口袋残基”所有原子的PDB格式**字符串**。这个字符串代表了被“切割”出来的、与配体紧密相关的结合口袋结构，它将在内存中被传递给下一步，而不是保存为文件。

### 第2步：解析口袋结构并转换为可用数据 (`pdb2data`函数)

上一步得到的口袋PDB字符串还只是文本，需要将其转换为程序可以操作的结构化数据。

*   **输入**: `make_pocket`函数返回的口袋PDB字符串。
*   **过程**:
    1.  使用RDKit的`Chem.MolFromPDBBlock`函数将PDB字符串转换为一个RDKit分子对象 (`pocket_mol`)。这一步能让RDKit自动推断原子间的共价键。
    2.  再次解析PDB字符串，将每个重原子的详细信息（原子名、残基名、三维坐标、新分配的索引）存储到一个字典（`pocket_heavy`）中，方便快速查找。
*   **输出**: 一个RDKit分子对象(`pocket_mol`)和一个包含所有口袋原子详细信息的字典(`pocket_heavy`)。

### 第3步：为口袋中的每个原子生成数字“指纹” (`Featurize.AtomNode`函数)

这是将化学信息转换为机器学习语言的核心步骤。脚本会为口袋中的每一个原子计算一个详细的特征向量。

*   **输入**: `pdb2data`返回的`pocket_mol`和`pocket_heavy`。
*   **过程**: 脚本为每个原子生成一个由多个one-hot编码向量拼接而成的长向量，包含：
    *   **通用化学特征**: 原子类型（C, O, N...）、成键数、杂化类型、形式电荷等。
    *   **蛋白质特有特征**: 这是最关键的一点。脚本通过`pocket=True`参数触发特定逻辑，为蛋白质原子计算**疏水性**特征。它会使用原子名（如'CA', 'CB'）和残基名（如'LEU', 'TRP'）作为钥匙，在一个巨大的、预先定义好的`prot_atom_hydrophobicity`字典（脚本顶部47-706行）中查询该原子的疏水性值（0或1）。这个特征是专门为蛋白质原子设计的。
    *   **药效团特征**: 判断原子是否为氢键供体、受体、酸性或碱性基团。
*   **输出**: 一个列表，其中每个元素都是代表一个蛋白质原子的特征向量（`node_pocket`），以及这些原子的坐标列表。

### 第4步：为口袋内的共价键生成特征 (`Featurize.CovalentEdge`函数)

不仅原子有特征，原子之间的连接（化学键）也有特征。

*   **输入**: `pocket_mol`和`pocket_heavy`。
*   **过程**:
    1.  脚本遍历`pocket_mol`中所有的共价键。
    2.  **核心逻辑**: `CommonEdge`辅助函数被调用，因为是蛋白质内部的键，所以它会返回`[1, 0, 0]`这个标识向量。这个向量会被加到每个键的特征向量的开头，明确地告诉GNN模型：“这是一条蛋白质内部的共价键”。
    3.  其他键的属性，如键类型（单键、双键等）、是否在环内等，也被编码并拼接到特征向量中。
*   **输出**: 口袋内所有共价键的连接关系（`edge_index_pocket`）和对应的特征向量列表（`edge_attr_pocket`）。

### 最终整合

经过以上步骤，原始的蛋白质PDB文件被成功转化为GNN模型所需的**节点特征**和**边特征**。这些信息随后会与同样方式处理过的**配体信息**，以及通过计算得到的**蛋白质-配体间非共价相互作用边**的信息合并，共同构成一个完整的蛋白质-配体复合物图数据（`torch_geometric.data.Data`对象），最终被保存为`.pkl`文件用于模型训练。

---

## 9. 天然配体PDBQT文件处理流程详解

`input_module_my.py` 脚本处理天然配体（Native Ligand）的PDBQT文件是一个精确且多层次的过程。该流程与处理蛋白质PDB文件并行发生，但有其自身的关键步骤和逻辑，旨在将配体的化学和物理性质完全数字化。

整个流程的起点同样在`native_run`函数，它定义了天然配体文件的路径，即`native_ligand`变量（例如，`.../1e66/xxxxxx.in.pdbqt`）。这个文件包含了配体的3D结构以及对接软件所需的额外信息。

### 第1步：解析PDBQT文件，提取核心原子信息 (`poss2data`函数)

这是处理配体PDBQT文件的核心入口。与简单的PDB文件不同，PDBQT格式包含了部分电荷和特定原子类型，脚本需要精确地提取这些信息。

*   **输入**: 天然配体PDBQT文件的路径 (`native_ligand`)。
*   **过程**:
    1.  **双重解析**: 脚本巧妙地同时使用两个库来解析文件：
        *   **Meeko (`PDBQTMolecule.from_file`)**: 专门处理PDBQT格式的库，首先被用来将文件加载为一个`PDBQTMolecule`对象，以正确处理其特殊语法。
        *   **RDKit (`mol.export_rdkit_mol()`)**: 接着，脚本从Meeko对象中导出一个RDKit `Mol`对象。这一步至关重要，因为它能正确地推断出配体分子内部的**化学键连接关系**和原子的化学属性（如杂化类型）。
    2.  **提取PDBQT特有信息**: 脚本将Meeko对象转换回PDBQT格式的原始文本字符串，并逐行扫描。对于每一个`ATOM`或`HETATM`行，它会精确地从特定列中抽取出：
        *   **三维坐标** (x, y, z)
        *   **部分电荷 (Partial Charge)**: 描述原子局部电荷分布的关键物理化学属性。
        *   **AutoDock原子类型**: 如'A' (芳香碳), 'OA' (氧受体)等。
    3.  **数据整合**: 脚本创建一个数据字典（`ligand_data`），将RDKit对象中的原子与从PDBQT文本中提取出的部分电荷、坐标等信息一一对应起来。
*   **输出**: 一个包含化学键信息的RDKit `Mol`对象（`ligand_mol`）和一个包含了每个原子坐标及PDBQT特有属性的字典（`ligand_data`）。

### 第2步：为配体原子生成数字“指纹” (`Featurize.AtomNode`函数)

这一步与蛋白质原子的特征化类似，但有一个关键的不同点。

*   **输入**: 上一步产生的`ligand_mol`和`ligand_data`。
*   **过程**:
    1.  脚本通过`pocket=False`参数触发配体分子的特定处理逻辑。
    2.  **核心差异点（疏水性计算）**: 对于蛋白质，疏水性是通过查表得到的。但对于配体，脚本采用了不同的策略：`hydrophobicity = 0 if abs(data[idx][1]) < 0.2 else 1`。这里的`data[idx][1]`就是上一步从PDBQT文件中提取的**部分电荷**。这条逻辑意味着，如果一个原子的局部电荷非常接近中性（绝对值小于0.2），它就被认为是疏水的；否则，如果它带有显著的正电荷或负电荷，它就被认为是亲水的。
    3.  其他通用化学特征和药效团特征的计算方式与蛋白质原子相同。
*   **输出**: 代表配体所有原子的节点特征向量列表 (`node_ligand`)。

### 第3步：为配体内部共价键生成特征 (`Featurize.CovalentEdge`函数)

*   **输入**: 配体的RDKit `Mol`对象。
*   **过程**:
    1.  脚本遍历配体分子中所有的共价键。
    2.  **核心逻辑**: 通过`pocket=False, ligand=True`参数，`CommonEdge`辅助函数返回`[0, 1, 0]`标识向量。这个向量被拼接到每个键特征向量的开头，明确地告诉GNN模型：“这是一条配体内部的共价键”，从而与蛋白质内部的键（标识为`[1, 0, 0]`）和蛋白质-配体间的相互作用（标识为`[0, 0, 1]`）区分开来。
*   **输出**: 配体内部所有共价键的连接关系和特征向量。

### 第4步：最终整合与“黄金标准”标记

*   **输入**: 处理好的蛋白质和配体的节点与边特征。
*   **过程**:
    1.  在`make_graph`函数中，脚本将处理好的蛋白质信息和配体信息合并。
    2.  **关键标记**: 因为这是一个**天然构象**，脚本会为其赋予两个最重要的“黄金标准”标签：
        *   **RMSD**: `y = torch.tensor([[0.0]])`。RMSD值为0，代表这是最准确、最真实的构象。
        *   **结合亲和力**: `bind=(native_bind)`。脚本从外部数据文件（`PL_data_txt`）中查找该PDB ID对应的实验结合亲和力数值，并将其作为另一个标签。
    3.  最后，所有信息被打包成一个`Data`对象，并根据命名规则`f"{pdb}_0_0"`（`form_id=0`代表天然构象）赋予其唯一的名称。
*   **输出**: 一个完整的、带有“真实答案”标签的图数据对象，用于后续的模型训练和评估。

---

## 10. Redocked Ligand DLG文件处理流程详解

`input_module_my.py` 脚本对redocked ligand的DLG文件有着专门的处理流程，这个过程涉及聚类信息提取、RMSD计算和结合亲和力数据整合等多个关键步骤。

### 10.1. DLG文件处理的主要入口

当处理redocked ligand时，脚本通过`_graph()`函数的特定分支进行处理：

*   **输入识别**: 当`dock=True`参数被传入时，脚本识别这是一个redocked ligand的处理请求。
*   **双重解析**: 脚本会两次调用`PDBQTMolecule.from_file(ligand, is_dlg=True)`来解析DLG文件，第一次用于RMSD计算，第二次用于图构建。
*   **构象标识**: 所有redocked构象被赋予`form_id = 1`，用于在最终的数据文件命名中区分构象类型。

### 10.2. Cluster信息的利用策略

**Cluster信息的使用具有选择性**：

*   **Redocked ligand处理**: 对于redocked ligand（`dock=True`），脚本设置`clist = None`，这意味着**处理DLG文件中的所有构象**，不进行聚类筛选。这样做的目的是为了获得尽可能多的训练样本，涵盖从优秀到较差的各种对接姿态。

*   **Decoys/Cross处理**: 相比之下，对于诱骗分子（`decoys=True`）和交叉对接分子（`cross=True`），脚本会调用`cluster_list()`函数：
    *   该函数解析DLG文件中的RMSD表格，寻找标记为`'0.00'`的聚类中心构象。
    *   最多提取100个聚类中心构象，然后只选择第一个（`clist = [1]`）进行处理。
    *   这种策略确保负样本的代表性，同时控制数据量。

### 10.3. RMSD数值的精确计算

**RMSD值通过专门的算法实时计算**：

*   **计算函数**: `dlg_2_rmsd()`函数负责计算每个redocked构象相对于native构象的RMSD。
*   **算法核心**: 
    1.  脚本首先通过`poss2data()`函数分别解析redocked构象和native构象的原子坐标。
    2.  对于每个redocked构象中的原子，脚本会在native构象中寻找**相同原子类型**的对应原子。
    3.  计算两个对应原子之间的欧几里得距离的平方，并找出最小值（处理原子匹配的歧义性）。
    4.  将所有原子的最小距离平方求和，除以原子数，再开平方根得到RMSD。

*   **数据来源**: RMSD不是从DLG文件中直接读取的，而是基于三维坐标实时计算的，确保了数值的准确性和一致性。

### 10.4. Binding Affinity数据的双重来源

**脚本使用了两种不同来源的结合亲和力数据**：

#### 来源1: DLG文件中的对接得分
*   **提取位置**: 在`poss2data()`函数中，脚本扫描PDBQT字符串中以`"USER    Estimated Free Energy of Binding"`开头的行。
*   **数据性质**: 这些是对接软件（如AutoDock Vina）计算的理论结合能，单位通常为kcal/mol。
*   **使用场景**: 主要用于诱骗分子和交叉对接分子的`bind`属性。

#### 来源2: 实验结合亲和力数据
*   **数据文件**: 从`PL_data_txt`参数指定的文件中读取（通常是`INDEX_general_PL_data.2020`）。
*   **数据转换**: 脚本读取实验测定的结合常数，并通过公式`float(line.split()[3])*-1.372574`转换为kcal/mol单位。
*   **质量控制**: 脚本会跳过包含`"<"`或`">"`符号的不确定数据。
*   **使用场景**: 专门用于native构象和redocked构象的`bind`属性，提供"黄金标准"的真实结合亲和力。

### 10.5. 数据整合与标签分配

**最终的数据对象构建遵循严格的标签分配规则**：

*   **RMSD标签 (`y`)**: 对于redocked构象，`y`值来自实时计算的RMSD，范围通常在0-10Å之间。
*   **结合亲和力标签 (`bind`)**: 
    *   **Redocked构象**: 使用实验测定的`native_bind`值，确保与native构象具有相同的"真实"结合亲和力基准。
    *   **其他构象**: 使用从DLG文件提取的对接得分`dock_score[_]`。
*   **构象命名**: 遵循`f"{pdb}_{form_id}_{num+_}"`格式，其中`form_id=1`明确标识这是redocked构象。

### 10.6. 训练意义与模型学习目标

这种精心设计的redocked ligand处理流程使得模型能够学习到：

1.  **姿态质量评估**: 通过RMSD标签，模型学会区分"好"的对接姿态（低RMSD）和"坏"的对接姿态（高RMSD）。
2.  **结合亲和力预测**: 通过实验结合亲和力标签，模型学会预测真实的分子结合强度。
3.  **对接软件校正**: 模型可以学会校正对接软件的系统性偏差，提高虚拟筛选的准确性。

这种多层次的标签体系为Akscore2模型提供了丰富的监督信号，使其在药物发现的实际应用中表现出色。
