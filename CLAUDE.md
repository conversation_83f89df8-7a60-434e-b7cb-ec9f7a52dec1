# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

本仓库包含 Akscore2 的训练和测试代码，这是一个用于预测蛋白质-配体相互作用的图神经网络模型。代码库似乎专注于药物发现中的虚拟筛选任务。

根据 `README.md`，有几个不同版本的模型：
- **v4**: Non-Dock
- **v7**: DockS
- **v8**: DockC

该研究的参考文献是："Hong, Y., Ha, J., Sim, J. et al. Accurate prediction of protein–ligand interactions by combining physical energy functions and graph-neural networks. J Cheminform 16, 121 (2024)."

## 代码结构

代码主要由 Python 脚本构成，使用 PyTorch 和 PyTorch Geometric 库。

- `train_akscore2_*.py`: 用于训练不同版本模型的脚本。这些脚本定义了模型架构（主要是 GATv2）、数据加载器和训练循环。
- `test_akscore2_*_dude_screen_pred.py`: 用于使用训练好的模型在 DUD-E 数据集上进行虚拟筛选预测的脚本。
- `slurm_*.sh`: 用于在 Slurm 集群上提交训练和测试作业的 Shell 脚本。这些文件包含了运行 Python 脚本所需的命令行参数。
- `ensemble_dude_results.py`: 可能是用于整合不同模型预测结果的脚本。
- `dude_cal_EF.py`: 可能是用于计算富集因子（Enrichment Factor, EF）的脚本，这是评估虚拟筛选性能的常用指标。

数据似乎是以 pickle 文件 (`.pkl` 或 `.pkl.gz`) 的形式存储的图对象。

## 如何运行

命令和路径是从 `slurm_train.sh` 和 `slurm_test.sh` 文件中推断出来的。实际路径可能需要根据本地环境进行调整。

### 模型训练

以下是如何训练 v4 模型的一个示例命令。可以根据 `slurm_train.sh` 中的其他命令调整参数以训练其他模型版本。

```bash
python train_akscore2_v4.py \
    --data_dir /path/to/your/data \
    --train_list_path /path/to/your/train_list.txt \
    --valid_list_path /path/to/your/valid_list.txt \
    --model_save_dir /path/to/save/models \
    --node_dim 72 \
    --edge_dim 12 \
    --hidden_dim 256 \
    --num_layers 5 \
    --dropout 0.1 \
    --batch_size 32 \
    --lr 0.0001
```

### 模型测试/筛选预测

以下是如何使用训练好的 v4 模型进行筛选预测的示例。

```bash
python test_akscore2_v4_dude_platform_screen_pred.py \
    --screen_dir /path/to/dude/dataset \
    --target_name <target_protein_name> \
    --model_path /path/to/your/trained_model.pth \
    --result_save_dir /path/to/save/results \
    --node_dim 72 \
    --edge_dim 12 \
    --hidden_dim 256 \
    --num_layers 5
```

## 训练脚本详解 (供深度学习初学者参考)

这三个脚本 (`train_akscore2_v4.py`, `train_akscore2_v7_bind_rmsd.py`, `train_akscore2_v8.py`) 代表了 `Akscore2` 模型开发过程中的三个不同阶段或版本。它们都基于图神经网络（GNN）来预测蛋白质和配体（小分子）之间的相互作用，但它们在模型架构、训练目标和数据处理方式上有所不同。

### 核心概念解析 (深度学习入门)

对于刚接触深度学习的同事，理解以下几个概念是关键：

1.  **PyTorch & PyTorch Geometric (PyG)**:
    *   `torch`: 是一个核心的深度学习框架，提供了构建神经网络所需的基本工具，例如张量（`torch.Tensor`，可以看作是能利用GPU加速的多维数组）和自动求导机制。
    *   `torch.nn.Module`: 是所有神经网络模型的基类。我们通过继承这个类来定义自己的模型架构。它的 `forward` 方法定义了数据如何通过网络层进行计算。
    *   `torch_geometric`: 是一个专门为图神经网络（GNN）构建的PyTorch扩展库。它提供了如图卷积层（如 `GATv2Conv`）、图数据结构（`Data`）和高效加载图数据的工具（`DataLoader`）等。

2.  **图神经网络 (GNN) 基础**:
    *   **什么是图？**: 在这个项目中，一个“图”代表一个蛋白质-配体复合物。图由“节点”（`nodes`）和“边”（`edges`）组成。
        *   **节点 (Nodes)**: 代表原子。每个节点都有一系列特征（`node features`），比如原子类型、电荷等。
        *   **边 (Edges)**: 代表原子之间的关系，比如化学键或空间上的邻近关系。每条边也可以有特征（`edge features`），比如键的类型或原子间的距离。
    *   **GNN 如何工作**: GNN 的核心思想是通过“消息传递”来更新节点的表示。在每一层，每个节点会聚合其邻居节点和连接边的信息，然后用这些聚合来的信息来更新自身的特征表示。经过多层堆叠，每个节点的最终表示就包含了其在图中的局部和更广泛的邻域信息。

3.  **模型训练流程**:
    *   **Dataset & DataLoader**:
        *   `Dataset` 类 (如 `akscore2_dataset`) 负责加载和预处理数据。它的 `__getitem__` 方法定义了如何获取单个数据样本。
        *   `DataLoader` 则将 `Dataset` 包装起来，自动处理数据的批处理（batching）、打乱（shuffling）和并行加载，为模型训练提供方便的数据流。
    *   **训练循环 (Training Loop)**:
        1.  **前向传播 (Forward Pass)**: 将一批数据输入模型，执行 `forward` 方法，得到模型的预测输出。
        2.  **计算损失 (Loss Calculation)**: 将模型的预测结果与真实标签（ground truth）进行比较，计算出一个“损失值”。损失值越小，表示模型预测得越准。这里用了 `MSELoss`（均方误差，用于回归任务）和 `BCEWithLogitsLoss`（带 Sigmoid 的二元交叉熵，用于二分类任务）。
        3.  **反向传播 (Backward Pass)**: PyTorch 会自动计算损失相对于模型所有参数的梯度（导数）。这个梯度指明了参数应该如何调整才能使损失变小。
        4.  **更新参数 (Optimizer Step)**: 优化器（如 `Adam`）根据计算出的梯度来微调模型的参数。
    *   这个过程会重复很多轮（`epoch`），直到模型的性能不再提升。

---

### 各版本训练脚本详解

下面我们来深入分析 `v4`, `v7`, `v8` 三个版本的具体实现和区别。

#### 1. `train_akscore2_v4.py` (Non-Dock 版本)

这个版本似乎是一个早期的、相对简单的模型。

*   **模型架构 (`GATv2`)**:
    *   **分离式处理**: 这个模型的独特之处在于它有两套独立的GNN层：`protein_gnn_layers` 和 `ligand_gnn_layers`。
    *   **信息聚合**: 它首先分别在蛋白质图和配体图上运行GNN，然后使用 `global_mean_pool` 将每个图的所有节点特征聚合成一个单一的向量（`protein_x_feat`, `ligand_x_feat`），代表整个蛋白质或配体的全局表示。
    *   **最终预测**: 最后，它将这两个全局向量拼接（`concat`）起来，输入到一个全连接网络（`graph_dec_rmsd`）中，最终输出一个值 `rmsd_logits`。

*   **训练目标**:
    *   该模型只预测一个任务：判断一个配体构象（pose）的好坏。
    *   它将这个问题简化为一个**二分类任务**。`rmsd_cutoff` 参数（例如 2Å）被用作阈值。如果一个构象的真实RMSD（与晶体结构相比的偏差）低于这个阈值，它被认为是“好”的（标签为1）；否则认为是“坏”的（标签为0）。
    *   损失函数 `BCEWithLogitsLoss` 就是为此类二分类任务设计的。

*   **数据处理 (`akscore2_dataset`)**:
    *   数据加载相对直接。它创建一个包含所有训练样本路径的扁平列表 `self.train_paths`，然后随机打乱。
    *   `get` 方法对于训练集是加载单个图，对于验证集则是加载与一个PDB ID相关的所有图。

*   **小结**: v4 是一个**分离式、单任务的分类模型**。它先独立学习蛋白质和配体的特征，然后组合起来进行判断。`README.md` 中称之为 "Non-Dock"，可能是因为它没有直接利用对接（docking）过程中产生的复合物图结构信息，而是将蛋白质和配体分开处理。

#### 2. `train_akscore2_v7_bind_rmsd.py` (DockS 版本)

这个版本比 v4 复杂得多，引入了多任务学习和更复杂的损失函数。`README.md` 称之为 "DockS"。

*   **模型架构 (`GATv2`)**:
    *   **一体化处理**: 与v4不同，v7只有一个GNN层集合 `protein_ligand_gnn_layers`。它直接在完整的蛋白质-配体复合物图上进行消息传递。这允许模型直接学习原子间的相互作用。
    *   **多任务输出**: GNN处理后的全局图特征 `protein_ligand_x_dock` 被同时输入到两个不同的“头”网络中：
        1.  `graph_dec_bind`: 预测结合亲和力（binding affinity），这是一个**回归任务**。
        2.  `graph_dec_rmsd`: 预测RMSD值，也是一个**回归任务**。
    *   这种架构让模型能够同时学习“这个配体能结合多紧？”（亲和力）和“这个对接姿势有多准？”（RMSD）。

*   **训练目标与损失函数**:
    *   这是一个**多任务回归模型**，损失函数 `total_loss` 是三个部分的加和：
        1.  `loss_bind_native_mse`: 对“天然构象”（真实结合模式）的结合亲和力进行预测，使用MSE（均方误差）损失。
        2.  `loss_bind_cross_random`: 一个巧妙的设计。对于那些已知的“非活性”或“随机”的配体（cross/random decoys），模型不要求预测一个精确的亲和力值，而是通过一个类似铰链损失的函数，惩罚那些被预测为亲和力过强（例如预测值小于-5）的样本。这教会模型将非活性分子的结合能预测得比较低。
        3.  `loss_rmsd`: 对“天然构象”和“对接构象”（docked poses）的RMSD进行预测，同样使用MSE损失。

*   **数据处理 (`akscore2_dataset`)**:
    *   **数据采样更复杂**: `get` 方法在训练时，除了加载一个PDB的天然构象，还会从其他PDB ID中随机采样多种类型的“干扰项”（decoys），包括对接构象、交叉构象和随机构象。这使得训练过程更具挑战性，迫使模型学习区分细微差别。
    *   **数据增强**: 对结合亲和力标签进行随机扰动（`graph.bind = graph.bind + np.random.uniform(...)`），这是一种常见的数据增强技术，可以提高模型的泛化能力。

*   **小结**: v7 是一个**一体化、多任务的回归模型**。它直接在复合物图上学习，并同时预测结合亲和力和RMSD。其复杂的损失函数和数据采样策略是为了让模型能更好地在虚拟筛选中排序和区分不同类型的配体。

#### 3. `train_akscore2_v8.py` (DockC 版本)

v8 在 v7 的基础上进行了微调和简化，似乎是想更专注于结合亲和力的预测。`README.md` 称之为 "DockC"。

*   **模型架构 (`GATv2`)**:
    *   **回归单任务**: 模型架构与v7类似（一体化处理），但它**移除了RMSD预测头** (`graph_dec_rmsd`)。现在模型只专注于预测结合亲和力 (`bind_logits`)。

*   **训练目标与损失函数**:
    *   目标是**只预测结合亲和力**。
    *   损失函数 `total_loss` 仍然是三部分之和，但与v7略有不同：
        1.  `loss_bind_native_mse`: 与v7相同。
        2.  `loss_bind_cross_random`: 与v7相同。
        3.  `loss_bind_dock_mse`: **这是新的部分**。它取代了v7中的 `loss_rmsd`。现在模型也需要对“对接构象”（docked poses）的结合亲和力进行预测。

*   **数据处理 (`akscore2_dataset`)**:
    *   **核心创新点**: 在数据增强部分 (`graph_rmsd_bindaff_aug`)，有一个非常关键的改动：
        ```python
        if name_splited[-2] == '1': ###if decoy dock
            graph.bind = graph.bind + graph.y
        ```
        `graph.y` 是RMSD值。这意味着，对于对接产生的构象，其“伪标签”结合亲和力被人为地调高了（因为结合能是负数，加上一个正的RMSD值会使其变得“不那么好”）。这是一种非常聪明的**标签增强**方法，它将姿势的好坏（RMSD）信息编码到了结合亲和力的训练目标中。模型被教导：一个RMSD越大的（越不准的）构象，其结合亲和力就越差。

*   **小结**: v8 是一个**一体化、单任务（但目标被增强）的回归模型**。它去掉了直接的RMSD预测，而是通过修改训练标签的方式，将RMSD信息隐式地融入到结合亲和力的预测任务中。这可能使得模型学习到的结合能打分函数能更好地惩罚不正确的对接姿势。

### 总结与对比

| 特性 | `train_akscore2_v4.py` (Non-Dock) | `train_akscore2_v7_bind_rmsd.py` (DockS) | `train_akscore2_v8.py` (DockC) |
| :--- | :--- | :--- | :--- |
| **模型范式** | 分离式 GNN (蛋白/配体分开) | 一体化 GNN (复合物整体) | 一体化 GNN (复合物整体) |
| **学习任务** | 单任务：姿势分类 (好/坏) | 多任务：结合能回归 + RMSD回归 | 单任务：结合能回归 |
| **损失函数** | 二元交叉熵损失 (BCE) | 复合损失 (MSE-native, Hinge-decoys, MSE-rmsd) | 复合损失 (MSE-native, Hinge-decoys, MSE-dock) |
| **核心创新点** | 简单的基线模型 | 同时预测结合能和姿势质量 | **将RMSD信息编码进结合能的训练标签中** |
| **数据处理** | 简单的训练集列表 | 复杂的数据采样策略，引入多种干扰项 | 与v7类似，但有关键的标签增强步骤 |
